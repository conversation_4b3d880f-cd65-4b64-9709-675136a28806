# 京东链接回复功能说明

## 功能概述

现在系统已经配置为**只回复包含京东链接的消息**，其他所有消息都会被忽略不回复。

## 主要特性

### 1. 智能京东链接检测
系统能够检测以下类型的京东链接和关键词：

**京东域名：**
- `jd.com` - 京东主站
- `m.jd.com` - 京东手机版
- `item.jd.com` - 京东商品页面
- `mall.jd.com` - 京东商城
- `3.cn` - 京东短链接
- `u.jd.com` - 京东推广链接
- `wqs.jd.com` - 京东其他服务

**京东关键词：**
- "京东"
- "JD.COM"
- "jd.com"

### 2. 自动API调用
当检测到京东链接时，系统会：
1. 发送等待消息："请稍等，正在查询京东商品信息..."
2. 调用京东API获取商品信息
3. 返回API结果或默认失败消息

### 3. 配置化管理
所有设置都在 `config.py` 文件中，可以灵活调整：

```python
# 回复模式配置
REPLY_MODE = {
    "ONLY_JD_LINKS": True,        # 只回复京东链接
    "REPLY_OTHER_LINKS": False,   # 不回复其他链接
    "REPLY_TEXT_MESSAGES": False, # 不回复普通文本
    "REPLY_PRICE_MESSAGES": False # 不回复价格消息
}
```

## 修改的文件

### 1. `main.py`
- 添加了配置文件导入
- 修改了消息处理逻辑，只处理京东链接
- 增强了日志记录

### 2. `utils/url_processor.py`
- 新增 `contains_jd_url()` 函数
- 改进了 `process_jd_url()` 函数
- 使用配置文件中的设置

### 3. `config.py` (新增)
- 集中管理所有配置选项
- 可以轻松调整回复行为

### 4. `test_jd_detection.py` (新增)
- 测试京东链接检测功能
- 验证API调用

## 使用方法

### 1. 运行测试
首先测试京东链接检测功能：
```bash
python test_jd_detection.py
```

### 2. 启动系统
```bash
python main.py
```

### 3. 测试消息
发送以下类型的消息测试：

**会回复的消息：**
- `https://item.jd.com/12345.html`
- `这是京东的商品链接`
- `去JD.COM看看`

**不会回复的消息：**
- `价格怎么样？`
- `还有其他颜色吗？`
- `https://www.taobao.com/item/12345`

## 配置选项详解

### 回复模式 (REPLY_MODE)
```python
REPLY_MODE = {
    "ONLY_JD_LINKS": True,        # 是否只回复京东链接
    "REPLY_OTHER_LINKS": False,   # 是否回复其他链接
    "REPLY_TEXT_MESSAGES": False, # 是否回复普通文本
    "REPLY_PRICE_MESSAGES": False # 是否回复价格相关消息
}
```

### 京东配置 (JD_CONFIG)
```python
JD_CONFIG = {
    "API_URL": "https://jkl.duizhew.com/api/jingdong/index",
    "JD_DOMAINS": ["jd.com", "m.jd.com", ...],
    "JD_KEYWORDS": ["京东", "JD.COM", "jd.com"],
    "WAITING_MESSAGE": "请稍等，正在查询京东商品信息...",
    "DEFAULT_FAIL_MESSAGE": "抱歉，暂时无法获取该京东商品的信息，请稍后再试。"
}
```

### 日志配置 (LOG_CONFIG)
```python
LOG_CONFIG = {
    "LOG_IGNORED_MESSAGES": True,  # 是否记录被忽略的消息
    "LOG_JD_API_CALLS": True       # 是否记录京东API调用
}
```

## 自定义配置

### 1. 修改京东域名列表
在 `config.py` 中的 `JD_CONFIG["JD_DOMAINS"]` 添加或删除域名。

### 2. 修改京东关键词
在 `config.py` 中的 `JD_CONFIG["JD_KEYWORDS"]` 添加或删除关键词。

### 3. 修改回复消息
在 `config.py` 中修改 `JD_CONFIG["WAITING_MESSAGE"]` 和 `JD_CONFIG["DEFAULT_FAIL_MESSAGE"]`。

### 4. 启用其他回复模式
如果需要回复其他类型的消息，可以修改 `REPLY_MODE` 中的设置：

```python
REPLY_MODE = {
    "ONLY_JD_LINKS": False,       # 关闭只回复京东链接
    "REPLY_OTHER_LINKS": True,    # 启用回复其他链接
    "REPLY_TEXT_MESSAGES": True,  # 启用回复普通文本
    "REPLY_PRICE_MESSAGES": True  # 启用回复价格消息
}
```

## 故障排除

### 1. 京东链接检测不准确
- 运行 `python test_jd_detection.py` 测试
- 检查 `config.py` 中的域名和关键词配置
- 查看日志中的匹配信息

### 2. 京东API调用失败
- 检查网络连接
- 验证API地址是否正确
- 查看API返回的错误信息

### 3. 消息没有回复
- 确认消息包含京东链接或关键词
- 检查 `config.py` 中的 `REPLY_MODE` 设置
- 查看日志中的处理信息

## 日志信息

系统会记录以下信息：
- 检测到的京东链接和匹配模式
- 被忽略的非京东消息
- 京东API调用结果
- 发送的回复消息

通过查看日志可以了解系统的运行状态和处理结果。
