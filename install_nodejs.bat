@echo off
chcp 65001 >nul
echo ========================================
echo Node.js 自动安装脚本
echo ========================================
echo.

:: 检查是否已安装Node.js
node --version >nul 2>&1
if %errorlevel% == 0 (
    echo Node.js 已安装
    node --version
    echo.
    goto :check_npm
) else (
    echo Node.js 未安装，开始下载安装...
    echo.
)

:: 创建临时目录
if not exist "%TEMP%\nodejs_install" mkdir "%TEMP%\nodejs_install"
cd /d "%TEMP%\nodejs_install"

:: 下载Node.js LTS版本
echo 正在下载 Node.js LTS 版本...
echo 下载地址: https://nodejs.org/dist/v18.19.0/node-v18.19.0-x64.msi

:: 使用PowerShell下载
powershell -Command "& {Invoke-WebRequest -Uri 'https://nodejs.org/dist/v18.19.0/node-v18.19.0-x64.msi' -OutFile 'nodejs.msi'}"

if exist "nodejs.msi" (
    echo 下载完成，开始安装...
    echo.
    echo 注意：安装过程中请：
    echo 1. 选择 "Add to PATH" 选项
    echo 2. 选择安装所有组件
    echo 3. 等待安装完成
    echo.
    pause
    
    :: 静默安装Node.js
    msiexec /i nodejs.msi /quiet /norestart
    
    echo 安装完成，请重新打开命令提示符以使PATH生效
) else (
    echo 下载失败，请手动下载安装 Node.js
    echo 下载地址: https://nodejs.org/
)

:check_npm
echo.
echo 检查npm...
npm --version >nul 2>&1
if %errorlevel% == 0 (
    echo npm 已安装
    npm --version
) else (
    echo npm 未安装或不可用
)

echo.
echo ========================================
echo 安装完成
echo ========================================
echo 请重新运行诊断脚本检查安装结果
pause
