// 兼容性更好的JavaScript版本，避免使用ES6+语法

// 生成mid
function generate_mid() {
    return "" + Math.floor(1e3 * Math.random()) + new Date().getTime() + " 0";
}

// 生成uuid
function generate_uuid() {
    return "-" + Date.now() + "1";
}

// 生成device_id
function generate_device_id(user_id) {
    var ee, et = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""), en = [], eo = Math.random, ei = 0;
    for (ei = 0; ei < 36; ei++) {
        if (8 === ei || 13 === ei || 18 === ei || 23 === ei) {
            en[ei] = "-";
        } else if (14 === ei) {
            en[ei] = "4";
        } else {
            ee = 0 | 16 * eo();
            en[ei] = et[19 === ei ? 3 & ee | 8 : ee];
        }
    }
    return en.join("") + "-" + user_id;
}

// 生成签名
function generate_sign(t, token, data) {
    var msg = token + "&" + t + "&" + "34839810" + "&" + data;
    // 简化的MD5实现或者返回固定值用于测试
    return "test_sign_" + msg.length;
}

// 简化的解密函数
function decrypt(data) {
    try {
        // 这里应该是复杂的解密逻辑，但为了兼容性，我们先返回原数据
        return data;
    } catch (e) {
        return "decrypt_error";
    }
}

// 测试函数
function test_functions() {
    console.log("generate_mid:", generate_mid());
    console.log("generate_uuid:", generate_uuid());
    console.log("generate_device_id:", generate_device_id("test123"));
    console.log("generate_sign:", generate_sign("123", "token", "data"));
}
