#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
JavaScript运行时诊断脚本
用于检测和解决JavaScript引擎兼容性问题
"""

import os
import sys
import subprocess
import platform

def print_separator(title):
    print("\n" + "="*50)
    print(f" {title} ")
    print("="*50)

def check_system_info():
    """检查系统信息"""
    print_separator("系统信息")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.architecture()}")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

def check_execjs():
    """检查execjs库"""
    print_separator("检查execjs库")
    try:
        import execjs
        print(f"execjs版本: {execjs.__version__}")
        print("execjs导入成功")
        
        # 检查可用的JavaScript运行时
        print("\n可用的JavaScript运行时:")
        runtimes = ['Node', 'V8', 'JScript', 'PhantomJS', 'Spidermonkey', 'JavaScriptCore']
        
        for runtime_name in runtimes:
            try:
                runtime = execjs.get(runtime_name)
                print(f"  ✓ {runtime_name}: 可用")
            except execjs.RuntimeUnavailableError:
                print(f"  ✗ {runtime_name}: 不可用")
        
        # 测试默认运行时
        try:
            default_runtime = execjs.get()
            print(f"\n默认运行时: {default_runtime}")
        except Exception as e:
            print(f"\n默认运行时错误: {e}")
            
    except ImportError as e:
        print(f"execjs导入失败: {e}")
        print("请运行: pip install pyexecjs")

def check_node_js():
    """检查Node.js"""
    print_separator("检查Node.js")
    try:
        result = subprocess.run(['node', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"Node.js版本: {result.stdout.strip()}")
        else:
            print("Node.js未安装或不在PATH中")
    except FileNotFoundError:
        print("Node.js未安装")
    except subprocess.TimeoutExpired:
        print("Node.js检查超时")
    except Exception as e:
        print(f"检查Node.js时出错: {e}")

def test_simple_js():
    """测试简单的JavaScript代码"""
    print_separator("测试JavaScript执行")
    try:
        import execjs
        
        # 简单的JavaScript代码
        simple_js = """
        function test_function() {
            return "Hello from JavaScript!";
        }
        
        function add_numbers(a, b) {
            return a + b;
        }
        """
        
        print("编译简单JavaScript代码...")
        ctx = execjs.compile(simple_js)
        
        print("测试函数调用...")
        result1 = ctx.call('test_function')
        print(f"test_function() 返回: {result1}")
        
        result2 = ctx.call('add_numbers', 5, 3)
        print(f"add_numbers(5, 3) 返回: {result2}")
        
        print("✓ JavaScript基本功能正常")
        
    except Exception as e:
        print(f"✗ JavaScript测试失败: {e}")

def test_project_js():
    """测试项目的JavaScript文件"""
    print_separator("测试项目JavaScript文件")
    
    # 检查文件是否存在
    js_files = [
        'static/xianyu_js_version_2.js',
        'static/xianyu_js_compatible.js'
    ]
    
    for js_file in js_files:
        print(f"\n检查文件: {js_file}")
        if os.path.exists(js_file):
            print(f"  ✓ 文件存在")
            print(f"  文件大小: {os.path.getsize(js_file)} 字节")
            
            # 尝试编译
            try:
                import execjs
                with open(js_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                ctx = execjs.compile(content)
                print(f"  ✓ 编译成功")
                
                # 测试函数调用
                try:
                    result = ctx.call('generate_mid')
                    print(f"  ✓ generate_mid() 调用成功: {result}")
                except Exception as e:
                    print(f"  ✗ generate_mid() 调用失败: {e}")
                    
            except Exception as e:
                print(f"  ✗ 编译失败: {e}")
        else:
            print(f"  ✗ 文件不存在")

def main():
    """主函数"""
    print("JavaScript运行时诊断工具")
    print("用于检测和解决JavaScript引擎兼容性问题")
    
    check_system_info()
    check_execjs()
    check_node_js()
    test_simple_js()
    test_project_js()
    
    print_separator("诊断完成")
    print("如果发现问题，请尝试以下解决方案:")
    print("1. 安装Node.js: https://nodejs.org/")
    print("2. 重新安装pyexecjs: pip uninstall pyexecjs && pip install pyexecjs")
    print("3. 如果问题持续，请使用兼容性JavaScript文件")

if __name__ == "__main__":
    main()
