#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东链接检测测试脚本
用于测试京东链接检测功能是否正常工作
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.url_processor import contains_jd_url, process_jd_url
from config import REPLY_MODE, JD_CONFIG

def test_jd_detection():
    """测试京东链接检测功能"""
    print("="*50)
    print("京东链接检测测试")
    print("="*50)
    
    # 测试用例
    test_cases = [
        # 京东链接
        ("https://item.jd.com/12345.html", True, "京东商品页面"),
        ("https://www.jd.com/product/12345", True, "京东主站链接"),
        ("https://m.jd.com/product/12345", True, "京东手机版链接"),
        ("https://3.cn/abc123", True, "京东短链接"),
        ("https://u.jd.com/abc123", True, "京东推广链接"),
        ("这是一个京东商品链接", True, "包含京东关键词"),
        ("JD.COM官方商城", True, "包含JD.COM"),
        ("去jd.com看看", True, "包含jd.com"),
        
        # 非京东链接
        ("https://www.taobao.com/item/12345", False, "淘宝链接"),
        ("https://www.tmall.com/item/12345", False, "天猫链接"),
        ("https://www.amazon.cn/item/12345", False, "亚马逊链接"),
        ("https://www.suning.com/item/12345", False, "苏宁链接"),
        ("这是一个普通的文本消息", False, "普通文本"),
        ("价格怎么样？", False, "价格询问"),
        ("还有其他颜色吗？", False, "商品咨询"),
    ]
    
    print(f"当前配置:")
    print(f"  只回复京东链接: {REPLY_MODE['ONLY_JD_LINKS']}")
    print(f"  京东域名列表: {JD_CONFIG['JD_DOMAINS']}")
    print(f"  京东关键词: {JD_CONFIG['JD_KEYWORDS']}")
    print()
    
    success_count = 0
    total_count = len(test_cases)
    
    for i, (text, expected, description) in enumerate(test_cases, 1):
        print(f"测试 {i}: {description}")
        print(f"  输入: {text}")
        print(f"  期望结果: {'是京东链接' if expected else '不是京东链接'}")
        
        try:
            result = contains_jd_url(text)
            print(f"  实际结果: {'是京东链接' if result else '不是京东链接'}")
            
            if result == expected:
                print(f"  ✓ 测试通过")
                success_count += 1
            else:
                print(f"  ✗ 测试失败")
        except Exception as e:
            print(f"  ✗ 测试异常: {e}")
        
        print()
    
    print("="*50)
    print(f"测试结果: {success_count}/{total_count} 通过")
    print(f"成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败，请检查配置")

def test_jd_api():
    """测试京东API调用"""
    print("="*50)
    print("京东API测试")
    print("="*50)
    
    # 测试API调用（使用示例链接）
    test_url = "https://item.jd.com/100012043978.html"
    print(f"测试链接: {test_url}")
    print("正在调用京东API...")
    
    try:
        result = process_jd_url(test_url)
        if result:
            print(f"✓ API调用成功")
            print(f"返回结果: {result}")
        else:
            print("✗ API调用失败或返回空结果")
    except Exception as e:
        print(f"✗ API调用异常: {e}")

def main():
    """主函数"""
    print("京东链接检测和API测试工具")
    print()
    
    # 测试链接检测
    test_jd_detection()
    
    print()
    
    # 询问是否测试API
    try:
        test_api = input("是否测试京东API调用？(y/n): ").lower().strip()
        if test_api in ['y', 'yes', '是']:
            test_jd_api()
    except KeyboardInterrupt:
        print("\n测试中断")

if __name__ == "__main__":
    main()
