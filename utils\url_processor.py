import re
import requests
from loguru import logger
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from config import JD_CONFIG
except ImportError:
    # 如果无法导入配置，使用默认配置
    JD_CONFIG = {
        "API_URL": "https://jkl.duizhew.com/api/jingdong/index",
        "JD_DOMAINS": ["jd.com", "m.jd.com", "item.jd.com", "mall.jd.com", "3.cn", "u.jd.com", "wqs.jd.com"],
        "JD_KEYWORDS": ["京东", "JD.COM", "jd.com"],
    }

def contains_url(text):
    """
    检查文本中是否包含URL链接

    Args:
        text (str): 需要检查的文本

    Returns:
        bool: 如果包含URL则返回True，否则返回False
    """
    # URL正则表达式模式
    url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
    return bool(re.search(url_pattern, text))

def contains_jd_url(text):
    """
    检查文本中是否包含京东链接

    Args:
        text (str): 需要检查的文本

    Returns:
        bool: 如果包含京东链接则返回True，否则返回False
    """
    # 构建京东域名的正则表达式模式
    domain_patterns = []
    for domain in JD_CONFIG["JD_DOMAINS"]:
        # 转义域名中的特殊字符
        escaped_domain = re.escape(domain)
        # 添加可选的www前缀和协议
        pattern = rf'https?://(?:www\.)?{escaped_domain}'
        domain_patterns.append(pattern)

    # 添加关键词模式
    keyword_patterns = []
    for keyword in JD_CONFIG["JD_KEYWORDS"]:
        keyword_patterns.append(re.escape(keyword))

    # 合并所有模式
    all_patterns = domain_patterns + keyword_patterns

    for pattern in all_patterns:
        if re.search(pattern, text, re.IGNORECASE):
            logger.info(f"检测到京东链接，匹配模式: {pattern}")
            return True

    return False

def process_jd_url(text):
    """
    处理包含京东URL的文本，将其提交到指定API

    Args:
        text (str): 包含URL的文本

    Returns:
        str: API返回的处理结果，如果处理失败则返回None
    """
    try:
        # 从配置获取API地址
        api_url = JD_CONFIG["API_URL"]
        payload = {'keyword': text}

        logger.info(f"正在提交URL到API: {api_url}")
        logger.debug(f"提交内容: {payload}")

        response = requests.post(api_url, data=payload, timeout=30)

        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            logger.debug(f"API响应: {result}")

            if result.get('status') == 200 and result.get('msg') == 'success':
                # 返回data.msg内容
                return result.get('data', {}).get('msg')
            else:
                logger.warning(f"API返回错误: {result}")
                return None
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            return None

    except requests.exceptions.Timeout:
        logger.error("API请求超时")
        return None
    except requests.exceptions.RequestException as e:
        logger.error(f"API请求异常: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"处理URL时发生错误: {str(e)}")
        return None
