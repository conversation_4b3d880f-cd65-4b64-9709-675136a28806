import re
import requests
from loguru import logger

def contains_url(text):
    """
    检查文本中是否包含URL链接
    
    Args:
        text (str): 需要检查的文本
        
    Returns:
        bool: 如果包含URL则返回True，否则返回False
    """
    # URL正则表达式模式
    url_pattern = r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+'
    return bool(re.search(url_pattern, text))

def process_jd_url(text):
    """
    处理包含京东URL的文本，将其提交到指定API
    
    Args:
        text (str): 包含URL的文本
        
    Returns:
        str: API返回的处理结果，如果处理失败则返回None
    """
    try:
        # 提交到指定API
        api_url = 'https://jkl.duizhew.com/api/jingdong/index'
        payload = {'keyword': text}
        
        logger.info(f"正在提交URL到API: {api_url}")
        logger.debug(f"提交内容: {payload}")
        
        response = requests.post(api_url, data=payload)
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            logger.debug(f"API响应: {result}")
            
            if result.get('status') == 200 and result.get('msg') == 'success':
                # 返回data.msg内容
                return result.get('data', {}).get('msg')
            else:
                logger.warning(f"API返回错误: {result}")
                return None
        else:
            logger.error(f"API请求失败，状态码: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"处理URL时发生错误: {str(e)}")
        return None
