# -*- coding: utf-8 -*-
"""
配置文件
用于控制机器人的回复行为
"""

# 回复模式配置
REPLY_MODE = {
    # 是否只回复包含京东链接的消息
    "ONLY_JD_LINKS": True,
    
    # 是否回复包含其他链接的消息
    "REPLY_OTHER_LINKS": False,
    
    # 是否回复普通文本消息
    "REPLY_TEXT_MESSAGES": False,
    
    # 是否回复价格相关消息
    "REPLY_PRICE_MESSAGES": False,
}

# 京东链接相关配置
JD_CONFIG = {
    # 京东API地址
    "API_URL": "https://jkl.duizhew.com/api/jingdong/index",
    
    # 京东链接检测的域名列表
    "JD_DOMAINS": [
        "jd.com",
        "m.jd.com", 
        "item.jd.com",
        "mall.jd.com",
        "3.cn",
        "u.jd.com",
        "wqs.jd.com",
    ],
    
    # 京东关键词列表
    "JD_KEYWORDS": [
        "京东",
        "JD.COM",
        "jd.com",
    ],
    
    # 等待消息
    "WAITING_MESSAGE": "请稍等，正在查询...",
    
    # 失败时的默认回复
    "DEFAULT_FAIL_MESSAGE": "抱歉，暂时无法获取，请稍后再试。",
}

# 日志配置
LOG_CONFIG = {
    # 是否记录忽略的消息
    "LOG_IGNORED_MESSAGES": True,
    
    # 是否记录京东API调用
    "LOG_JD_API_CALLS": True,
}

# 消息过滤配置
MESSAGE_FILTER = {
    # 消息时效性（毫秒）- 过滤多久之前的消息
    "MESSAGE_TIMEOUT": 300000,  # 5分钟
    
    # 是否过滤自己发送的消息
    "FILTER_SELF_MESSAGES": True,
}
