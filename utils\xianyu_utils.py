import json
import subprocess
from functools import partial
import os
import sys
subprocess.Popen = partial(subprocess.Popen, encoding="utf-8")
import execjs

def get_js_runtime():
    """获取最佳的JavaScript运行时"""
    # 尝试不同的JavaScript运行时，按优先级排序
    runtimes = ['Node', 'V8', 'JScript', 'PhantomJS']

    for runtime_name in runtimes:
        try:
            runtime = execjs.get(runtime_name)
            print(f"使用JavaScript运行时: {runtime_name}")
            return runtime
        except execjs.RuntimeUnavailableError:
            continue

    # 如果都不可用，使用默认运行时
    print("使用默认JavaScript运行时")
    return execjs.get()

def load_javascript_file():
    """加载JavaScript文件并编译"""
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取项目根目录
    project_root = os.path.dirname(current_dir)

    # JavaScript文件列表，按优先级排序
    js_files = [
        os.path.join(project_root, 'static', 'xianyu_js_version_2.js'),
        os.path.join(project_root, 'static', 'xianyu_js_compatible.js')
    ]

    print(f"Python版本: {sys.version}")
    print(f"操作系统: {os.name}")

    for js_file_path in js_files:
        print(f"尝试加载JavaScript文件: {js_file_path}")

        try:
            # 检查文件是否存在
            if not os.path.exists(js_file_path):
                print(f"文件不存在，跳过: {js_file_path}")
                continue

            # 读取文件内容
            with open(js_file_path, 'r', encoding='utf-8') as f:
                js_content = f.read()

            print(f"JavaScript文件大小: {len(js_content)} 字符")

            # 获取JavaScript运行时
            runtime = get_js_runtime()

            # 编译JavaScript代码
            compiled_js = runtime.compile(js_content)
            print(f"JavaScript编译成功: {js_file_path}")

            return compiled_js

        except execjs.ProgramError as e:
            print(f"JavaScript语法错误: {e}")
            print(f"文件: {js_file_path}")
            print("尝试下一个JavaScript文件...")
            continue
        except Exception as e:
            print(f"加载JavaScript文件时出错: {e}")
            print(f"文件: {js_file_path}")
            print("尝试下一个JavaScript文件...")
            continue

    # 如果所有文件都失败了
    raise RuntimeError("无法加载任何JavaScript文件，请检查文件是否存在或JavaScript引擎兼容性")

# 初始化JavaScript运行时
try:
    xianyu_js = load_javascript_file()
except Exception as e:
    print(f"初始化JavaScript运行时失败: {e}")
    xianyu_js = None

def trans_cookies(cookies_str):
    cookies = dict()
    for i in cookies_str.split("; "):
        try:
            cookies[i.split('=')[0]] = '='.join(i.split('=')[1:])
        except:
            continue
    return cookies


def safe_js_call(func_name, *args):
    """安全调用JavaScript函数"""
    if xianyu_js is None:
        raise RuntimeError("JavaScript运行时未初始化，请检查JavaScript文件和运行环境")

    try:
        print(f"调用JavaScript函数: {func_name}, 参数: {args}")
        result = xianyu_js.call(func_name, *args)
        print(f"JavaScript函数 {func_name} 执行成功")
        return result
    except execjs.ProgramError as e:
        print(f"JavaScript执行错误: {e}")
        print(f"函数: {func_name}, 参数: {args}")
        print("这可能是由于JavaScript引擎兼容性问题导致的")
        raise RuntimeError(f"JavaScript函数 {func_name} 执行失败: {e}")
    except Exception as e:
        print(f"调用JavaScript函数时发生未知错误: {e}")
        print(f"函数: {func_name}, 参数: {args}")
        raise RuntimeError(f"JavaScript函数 {func_name} 调用失败: {e}")

def generate_mid():
    return safe_js_call('generate_mid')

def generate_uuid():
    return safe_js_call('generate_uuid')

def generate_device_id(user_id):
    if not user_id:
        raise ValueError("user_id 不能为空")
    return safe_js_call('generate_device_id', user_id)

def generate_sign(t, token, data):
    return safe_js_call('generate_sign', t, token, data)

def decrypt(data):
    return safe_js_call('decrypt', data)
