# JavaScript兼容性问题解决方案

## 问题描述
在不同的Windows版本（Windows 10 vs Windows 11）上运行项目时，出现JavaScript语法错误：
```
execjs._exceptions.ProgramError: SyntaxError: 语法错误
```

## 问题原因
1. **JavaScript引擎差异**：不同Windows版本使用不同的JScript引擎
2. **Node.js版本差异**：系统中安装的Node.js版本不同或未安装
3. **execjs库兼容性**：execjs库在不同环境下的行为差异

## 解决方案

### 方案1：运行诊断脚本（推荐）
1. 运行诊断脚本检查环境：
   ```bash
   python diagnose_js_runtime.py
   ```

2. 根据诊断结果采取相应措施

### 方案2：安装Node.js
1. 手动安装Node.js：
   - 访问 https://nodejs.org/
   - 下载LTS版本
   - 安装时确保选择"Add to PATH"

2. 或者运行自动安装脚本：
   ```bash
   install_nodejs.bat
   ```

### 方案3：使用兼容性JavaScript文件
项目现在包含两个JavaScript文件：
- `static/xianyu_js_version_2.js` - 原始版本
- `static/xianyu_js_compatible.js` - 兼容性版本

系统会自动尝试加载，如果原始版本失败会自动切换到兼容性版本。

### 方案4：手动指定JavaScript运行时
如果问题持续，可以在代码中手动指定运行时：

```python
import execjs

# 尝试使用特定的运行时
try:
    # 优先使用Node.js
    runtime = execjs.get('Node')
except:
    try:
        # 备用JScript
        runtime = execjs.get('JScript')
    except:
        # 使用默认
        runtime = execjs.get()
```

## 修改的文件

### 1. `utils/xianyu_utils.py`
- 添加了JavaScript运行时检测
- 添加了多个JavaScript文件的备用加载机制
- 增强了错误处理和调试信息
- 添加了安全的JavaScript函数调用包装器

### 2. `main.py`
- 添加了环境变量验证
- 增强了错误处理
- 添加了用户友好的错误提示

### 3. 新增文件
- `static/xianyu_js_compatible.js` - 兼容性JavaScript文件
- `diagnose_js_runtime.py` - 诊断脚本
- `install_nodejs.bat` - Node.js安装脚本

## 使用步骤

1. **首先运行诊断**：
   ```bash
   python diagnose_js_runtime.py
   ```

2. **如果Node.js未安装**：
   ```bash
   install_nodejs.bat
   ```

3. **重新运行诊断确认**：
   ```bash
   python diagnose_js_runtime.py
   ```

4. **运行主程序**：
   ```bash
   python main.py
   ```

## 常见问题

### Q: 诊断脚本显示"Node.js未安装"
A: 运行 `install_nodejs.bat` 或手动安装Node.js

### Q: 所有JavaScript运行时都不可用
A: 
1. 重新安装pyexecjs：`pip uninstall pyexecjs && pip install pyexecjs`
2. 安装Node.js
3. 重启命令提示符

### Q: JavaScript编译成功但函数调用失败
A: 这通常是JavaScript代码兼容性问题，系统会自动切换到兼容性版本

## 技术细节

### JavaScript运行时优先级
1. Node.js（推荐）
2. V8
3. JScript（Windows默认）
4. PhantomJS

### 文件加载优先级
1. `xianyu_js_version_2.js`（原始版本）
2. `xianyu_js_compatible.js`（兼容性版本）

## 联系支持
如果问题仍然存在，请提供以下信息：
1. 诊断脚本的完整输出
2. 操作系统版本
3. Python版本
4. 错误的完整堆栈跟踪
